import { ProductItemV3 } from '@/services/InventoryManagementV3/product-item';
import { Department } from '@/services/vietplants/department';
import { Environment } from '@/services/vietplants/environment';
import { Plan } from '@/services/vietplants/plan';
import { YieldExpectedOutput } from '@/services/vietplants/production-plan-allocation';
import { Dayjs } from 'dayjs';

export interface EmployeesTableProps {
  plan: Plan | null;
  setDepartmentId: (id: string) => void;
  setTaskAssignmentSpecificEmployees: (data: any[]) => void;
  availableEnvironmentIds: string[];
  yieldOutputs: YieldExpectedOutput[];
  loadingYieldOutputs: boolean;
}

export interface TaskAssignmentColumnType {
  stt: number;
  key?: string | number;
  assigned_to_employee_fullName: string;
  time: string;
  task: string;
  status: string;
  priority_level: string;
  exp_quantity: number;
  environment_label: string;
  product: string;
  item_task: string;
  production_task: string;
  description: string;
  task_type?: string;
  // Allocation-related properties
  allocationValid?: boolean;
  allocationMessage?: string;
  matchingYieldOutput?: YieldExpectedOutput | null;
  assigned_to_employee_id?: string;
  // For existing tasks - include the name field for upsert operations
  name?: string;
}

export interface TaskTemplate {
  label: string;
  value: string;
  priority_level: string;
  environment_template_id: string;
  item_task: string[];
  production_task: string[];
  task_type: string;
}

export interface AllocationType {
  valid: boolean;
  message: string;
  matchingYieldOutput: YieldExpectedOutput | null;
}

export interface DateRange {
  dates: [Dayjs, Dayjs] | null;
  setDates: (dates: [Dayjs, Dayjs] | null) => void;
}

export interface DepartmentState {
  departmentList: { label: string; value: string | undefined }[];
  listAllDepartments: Department[];
  selectedDepartmentId: string | null;
  selectedDepartment: Department | null;
  setSelectedDepartmentId: (id: string | null) => void;
}

export interface EmployeeState {
  employeesList: { label: string; value: string }[];
  selectedEmployeeId: string | null;
  dataOfSpecificChosenEmployee: { [key: string]: any } | null;
  setSelectedEmployeeId: (id: string | null) => void;
}

export interface EnvironmentState {
  listOfAllEnvironmentTemplates: Environment[];
}

export interface TaskState {
  listOfTaskTemplates: TaskTemplate[];
  allTaskTemplates: TaskTemplate[];
  listOfAllItemTask: ProductItemV3[];
}

export interface AllocationState {
  allocationResults: Record<number, AllocationType>;
  validateAllocation: (
    taskId: number,
    productionTask: string,
    taskDate: string,
    quantity: number,
  ) => void;
}
