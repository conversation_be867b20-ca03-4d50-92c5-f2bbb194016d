/**
 * Filter utilities for AssignManyTasks EmployeesTable
 * Based on the same pattern as CheckingTable filters
 */

/**
 * Creates a like filter string for SQL queries
 */
export const getLikeFilter = (params: any) => {
  return `%${params}%`;
};

/**
 * Constructs filter parameters for existing task fetching API
 * Uses the same table name and filtering pattern as CheckingTable
 */
export function getFilterTasksForAssignment(params: {
  start_date?: string;
  end_date?: string;
  assigned_to?: string;
  department_id?: string;
  farming_plan_state?: string;
  environment_id?: string | string[];
  productionPlanId?: string;
  current?: number;
  pageSize?: number;
}) {
  console.log('[getFilterTasksForAssignment] Input params:', params);

  let filterArr: any[] = [];

  // Date range filters - using the same logic as CheckingTable
  if (params.start_date && params.end_date) {
    // Task overlaps with date range if:
    // task.start_date <= end_date AND task.end_date >= start_date
    filterArr.push(['iot_farming_plan_task', 'start_date', '<=', params.end_date]);
    filterArr.push(['iot_farming_plan_task', 'end_date', '>=', params.start_date]);
  } else if (params.start_date) {
    // If only start date provided, get tasks that end on or after this date
    filterArr.push(['iot_farming_plan_task', 'end_date', '>=', params.start_date]);
  } else if (params.end_date) {
    // If only end date provided, get tasks that start on or before this date
    filterArr.push(['iot_farming_plan_task', 'start_date', '<=', params.end_date]);
  }

  // Employee filter - using like filter as in CheckingTable
  if (params.assigned_to) {
    filterArr.push([
      'iot_farming_plan_task',
      'assigned_to',
      'like',
      getLikeFilter(params.assigned_to),
    ]);
  }

  // Department filter (if the API supports it)
  if (params.department_id) {
    filterArr.push(['iot_farming_plan_task', 'department_id', '=', params.department_id]);
  }

  // Farming plan state filter (if the API supports it)
  if (params.farming_plan_state) {
    filterArr.push(['iot_farming_plan_task', 'farming_plan_state', '=', params.farming_plan_state]);
  }

  // Environment filter (if the API supports it)
  if (params.environment_id) {
    // Handle both string and array of environment IDs
    const environmentIds = Array.isArray(params.environment_id)
      ? params.environment_id
      : [params.environment_id];

    // Only add filter if we have valid environment IDs
    if (environmentIds.length > 0 && environmentIds.some(id => id)) {
      filterArr.push(['iot_farming_plan_task', 'environment_template_id', 'in', environmentIds]);
    }
  }


  const returnObj: any = {
    page: params.current || 1,
    size: params.pageSize || 1000, // Get a large number to capture all relevant tasks
    filters: JSON.stringify(filterArr),
    productionPlanId: params.productionPlanId || '',
    order_by: 'start_date asc',
  };

  console.log('[getFilterTasksForAssignment] Generated filter object:', returnObj);
  console.log('[getFilterTasksForAssignment] Filter array:', filterArr);
  return returnObj;
}

/**
 * Transform existing task data to match TaskAssignmentColumnType structure
 * Based on ITaskManagerRes structure from getTaskManagerList API
 */
export function transformExistingTaskToAssignment(
  existingTask: any,
  stt: number,
  taskTemplates: any,
): any {
  console.log('[transformExistingTaskToAssignment] Processing task:', existingTask);

  // Handle date formatting - convert from timestamp to YYYY-MM-DD format
  const formatDate = (dateValue: any) => {
    if (!dateValue) return '';

    // If it's already a string in YYYY-MM-DD format, return as is
    if (typeof dateValue === 'string' && dateValue.match(/^\d{4}-\d{2}-\d{2}$/)) {
      return dateValue;
    }

    // If it's a string with time (like "2025-06-09 00:00:00"), extract date part
    if (typeof dateValue === 'string' && dateValue.includes(' ')) {
      return dateValue.split(' ')[0];
    }

    // If it's a timestamp or Date object, format it
    try {
      const date = new Date(dateValue);
      return date.toISOString().split('T')[0]; // Get YYYY-MM-DD part
    } catch (error) {
      console.warn('[transformExistingTaskToAssignment] Invalid date format:', dateValue);
      return '';
    }
  };

  // Get employee name from assigned_to_info (ITaskManagerRes structure)
  const getEmployeeName = () => {
    if (existingTask.assigned_to_info && existingTask.assigned_to_info.length > 0) {
      const assignedUser = existingTask.assigned_to_info[0];
      return `${assignedUser.last_name || ''} ${assignedUser.first_name || ''}`.trim();
    }
    return 'Unknown Employee';
  };

  // Find the matching task template to get the correct value for the Select component
  const matchingTaskTemplate = taskTemplates.find((template: any) => template.name === existingTask.template_id);

  console.log('[transformExistingTaskToAssignment] Looking for task template:', {
    template_id: existingTask.template_id,
    availableTemplates: taskTemplates.map((t: any) => ({ name: t.name, label: t.label })),
    matchingTemplate: matchingTaskTemplate,
  });

  const transformed = {
    stt: stt,
    key: `existing-${existingTask.name}`,
    assigned_to_employee_fullName: getEmployeeName(),
    assigned_to_employee_id: existingTask.assigned_to || '',
    time: formatDate(existingTask.start_date),
    task: existingTask.template_id || '', // Use template_id from the API response
    status: existingTask.status || '',
    priority_level: matchingTaskTemplate?.priority_level || '', // Get from matching template
    exp_quantity: existingTask.prod_quantity_list?.[0]?.exp_quantity || 0, // Extract from prod_quantity_list
    environment_label: existingTask.enviroment_template_label || '', // Use environment template label
    product: existingTask.prod_quantity_list?.[0]?.item_name,
    item_task: existingTask.item_list?.[0]?.item_name || '',
    production_task: existingTask.prod_quantity_list?.[0]?.item_name || '',
    description: existingTask.description || '',
    allocationValid: false,
    allocationMessage: '',
    matchingYieldOutput: null,
    task_type: existingTask.task_type || '', // Use task_type from API response
    // Include the name field for upsert operations
    name: existingTask.name,
  };

  console.log('[transformExistingTaskToAssignment] Input task:', existingTask);
  console.log('[transformExistingTaskToAssignment] Transformed task:', transformed);
  return transformed;
}
